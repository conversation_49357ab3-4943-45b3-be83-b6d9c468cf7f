<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tidal API Test</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background: #1a1a1a;
        color: white;
      }
      .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #333;
        border-radius: 8px;
        background: #2a2a2a;
      }
      button {
        background: #007acc;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #005a9e;
      }
      .result {
        margin-top: 10px;
        padding: 10px;
        background: #333;
        border-radius: 5px;
        white-space: pre-wrap;
        font-family: monospace;
        font-size: 12px;
        max-height: 300px;
        overflow-y: auto;
      }
      input {
        padding: 8px;
        margin: 5px;
        border: 1px solid #555;
        border-radius: 4px;
        background: #333;
        color: white;
        width: 300px;
      }
    </style>
  </head>
  <body>
    <h1>Tidal API Test</h1>

    <div class="test-section">
      <h2>1. Test Search Endpoint</h2>
      <input
        type="text"
        id="searchQuery"
        placeholder="Enter search query (e.g., That's So True Gracie Abrams)"
        value="That's So True Gracie Abrams"
      />
      <button onclick="testSearch()">Search</button>
      <div id="searchResult" class="result"></div>
    </div>

    <div class="test-section">
      <h2>2. Test Track by ID</h2>
      <input
        type="text"
        id="trackId"
        placeholder="Enter track ID"
        value="393536993"
      />
      <select id="quality">
        <option value="HIGH">HIGH (AAC 320kbps)</option>
        <option value="LOSSLESS" selected>LOSSLESS (FLAC)</option>
        <option value="HI_RES">HI_RES</option>
      </select>
      <button onclick="testTrackById()">Get Track</button>
      <div id="trackResult" class="result"></div>
    </div>

    <div class="test-section">
      <h2>3. Browser FLAC Support</h2>
      <button onclick="testFlacSupport()">Check FLAC Support</button>
      <div id="flacResult" class="result"></div>
    </div>

    <div class="test-section">
      <h2>4. Test Complete Flow</h2>
      <input
        type="text"
        id="completeQuery"
        placeholder="Enter search query"
        value="That's So True Gracie Abrams"
      />
      <button onclick="testCompleteFlow()">Search → Get Track → Play</button>
      <div id="completeResult" class="result"></div>
      <audio
        id="testAudio"
        controls
        style="width: 100%; margin-top: 10px; display: none"
      ></audio>
    </div>

    <script>
      const ENDPOINTS = [
        "/api/tidal",
        "/api/tidal-backup",
        "/api/tidal-vercel",
      ];

      let currentEndpoint = 0;

      function log(elementId, message) {
        const element = document.getElementById(elementId);
        element.textContent +=
          new Date().toLocaleTimeString() + ": " + message + "\n";
        element.scrollTop = element.scrollHeight;
      }

      function clearLog(elementId) {
        document.getElementById(elementId).textContent = "";
      }

      async function testSearch() {
        const query = document.getElementById("searchQuery").value;
        clearLog("searchResult");
        log("searchResult", `Testing search for: "${query}"`);

        try {
          const url = `${
            ENDPOINTS[currentEndpoint]
          }/api/tidal/search?q=${encodeURIComponent(query)}&limit=5`;
          log("searchResult", `URL: ${url}`);

          const response = await fetch(url);
          log("searchResult", `Response status: ${response.status}`);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }

          const data = await response.json();
          log("searchResult", `Results: ${JSON.stringify(data, null, 2)}`);

          if (data.items && data.items.length > 0) {
            log("searchResult", `\nFirst track ID: ${data.items[0].id}`);
            document.getElementById("trackId").value = data.items[0].id;
          }
        } catch (error) {
          log("searchResult", `Error: ${error.message}`);
        }
      }

      async function testTrackById() {
        const trackId = document.getElementById("trackId").value;
        const quality = document.getElementById("quality").value;
        clearLog("trackResult");
        log(
          "trackResult",
          `Testing track ID: ${trackId} with quality: ${quality}`
        );

        try {
          const url = `${ENDPOINTS[currentEndpoint]}/api/tidal/track/${trackId}?quality=${quality}`;
          log("trackResult", `URL: ${url}`);

          const response = await fetch(url);
          log("trackResult", `Response status: ${response.status}`);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }

          const data = await response.json();
          log("trackResult", `Response: ${JSON.stringify(data, null, 2)}`);

          // Check if it's array format
          if (Array.isArray(data) && data.length >= 3) {
            const urlObject = data.find((item) => item.OriginalTrackUrl);
            if (urlObject) {
              log(
                "trackResult",
                `\nStreaming URL found: ${urlObject.OriginalTrackUrl.substring(
                  0,
                  100
                )}...`
              );
            }
          }
        } catch (error) {
          log("trackResult", `Error: ${error.message}`);
        }
      }

      function testFlacSupport() {
        clearLog("flacResult");
        const audio = document.createElement("audio");
        const flacSupport = audio.canPlayType("audio/flac");
        const mp4Support = audio.canPlayType("audio/mp4");
        const oggSupport = audio.canPlayType("audio/ogg");

        log("flacResult", `FLAC support: ${flacSupport || "none"}`);
        log("flacResult", `MP4 support: ${mp4Support || "none"}`);
        log("flacResult", `OGG support: ${oggSupport || "none"}`);
        log("flacResult", `User Agent: ${navigator.userAgent}`);

        const supportsFlac = flacSupport.replace(/no/, "");
        const recommendedQuality = supportsFlac ? "LOSSLESS" : "HIGH";
        log("flacResult", `\nRecommended quality: ${recommendedQuality}`);
      }

      async function testCompleteFlow() {
        const query = document.getElementById("completeQuery").value;
        clearLog("completeResult");
        log("completeResult", `Testing complete flow for: "${query}"`);

        try {
          // Step 1: Search
          log("completeResult", "\n--- Step 1: Search ---");
          const searchUrl = `${
            ENDPOINTS[currentEndpoint]
          }/api/tidal/search?q=${encodeURIComponent(query)}&limit=5`;
          const searchResponse = await fetch(searchUrl);

          if (!searchResponse.ok) {
            throw new Error(`Search failed: HTTP ${searchResponse.status}`);
          }

          const searchData = await searchResponse.json();
          log(
            "completeResult",
            `Found ${searchData.totalNumberOfItems} results`
          );

          if (!searchData.items || searchData.items.length === 0) {
            throw new Error("No search results found");
          }

          const firstTrack = searchData.items[0];
          log(
            "completeResult",
            `Selected: "${firstTrack.title}" by ${
              firstTrack.artist?.name || "Unknown"
            }`
          );

          // Step 2: Get track details
          log("completeResult", "\n--- Step 2: Get Track Details ---");
          const audio = document.createElement("audio");
          const supportsFlac = audio
            .canPlayType("audio/flac")
            .replace(/no/, "");
          const quality = supportsFlac ? "LOSSLESS" : "HIGH";
          log(
            "completeResult",
            `Using quality: ${quality} (FLAC support: ${!!supportsFlac})`
          );

          const trackUrl = `${ENDPOINTS[currentEndpoint]}/api/tidal/track/${firstTrack.id}?quality=${quality}`;
          const trackResponse = await fetch(trackUrl);

          if (!trackResponse.ok) {
            throw new Error(`Track fetch failed: HTTP ${trackResponse.status}`);
          }

          const trackData = await trackResponse.json();

          // Step 3: Extract streaming URL
          log("completeResult", "\n--- Step 3: Extract Streaming URL ---");
          let streamingUrl = null;

          if (Array.isArray(trackData) && trackData.length >= 3) {
            const urlObject = trackData.find((item) => item.OriginalTrackUrl);
            if (urlObject) {
              streamingUrl = urlObject.OriginalTrackUrl;
              log(
                "completeResult",
                `Streaming URL found: ${streamingUrl.substring(0, 100)}...`
              );
            }
          }

          if (!streamingUrl) {
            throw new Error("No streaming URL found in response");
          }

          // Step 4: Test playback
          log("completeResult", "\n--- Step 4: Test Playback ---");
          const audioElement = document.getElementById("testAudio");
          audioElement.src = streamingUrl;
          audioElement.style.display = "block";

          audioElement.onloadedmetadata = () => {
            log(
              "completeResult",
              `Audio loaded successfully! Duration: ${audioElement.duration}s`
            );
          };

          audioElement.onerror = (e) => {
            log(
              "completeResult",
              `Audio error: ${e.target.error?.message || "Unknown error"}`
            );
          };

          log(
            "completeResult",
            "Audio element updated. Check the audio player below."
          );
        } catch (error) {
          log("completeResult", `Error: ${error.message}`);
        }
      }
    </script>
  </body>
</html>
